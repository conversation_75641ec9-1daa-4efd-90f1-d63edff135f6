/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
static MLX90393_Handle_t mlx_handle;
volatile uint8_t mlx90393_interrupt_flag = 0;  // MLX90393中断标志位
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Low Power System ===\r\n");
	printf("System will enter STOP mode after initialization\r\n");
	printf("Wake up by MLX90393 INT signal on PB0\r\n");
	printf("=====================================\r\n");

	// 初始化时关闭MLX90393电源（确保干净的启动状态）
	MLX90393_PW_OFF;
	printf("MLX90393 Power: OFF (Initial state)\r\n");
	RF_PWR_OFF;   // 关闭通信模块
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // ==================== 工作流程开始 ====================
    printf("\r\n=== Device Wake Up ===\r\n");

    // 声明GPIO配置结构体，整个循环中都会用到
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 检查是否是MLX90393中断唤醒，如果是则读取数据清除中断状态
    if (mlx90393_interrupt_flag) {
        mlx90393_interrupt_flag = 0;  // 清除标志位

        printf("MLX90393 interrupt detected, reading data to clear status...\r\n");
        MLX90393_Data_t interrupt_data;

        if (MLX90393_ReadSingleMeasurement(&mlx_handle, &interrupt_data) == MLX90393_OK) {
            printf("MLX90393 Interrupt Data: X=%.1f, Y=%.1f, Z=%.1f\r\n",
                   interrupt_data.x, interrupt_data.y, interrupt_data.z);
        } else {
            printf("MLX90393 Interrupt Data Read: FAILED\r\n");
        }

        // 读取状态寄存器确认中断已清除
        uint8_t status = MLX90393_GetStatus(&mlx_handle);
        printf("MLX90393 Status after read: 0x%02X\r\n", status);
    }

    // 1. 立即关闭MLX90393电源（如果之前触发了中断）
		RF_PWR_ON; //打开通信模块

    MLX90393_PW_OFF;
    printf("MLX90393 Power: OFF\r\n");

    // 2. 等待1秒让ADC稳定（MLX90393断电时间可根据需要调整）
    printf("Waiting 1 second for ADC stabilization...\r\n");
    HAL_Delay(3000);

    // 3. 采集ADC电压值
    uint16_t battery_voltage_mv = ADC_ReadBatteryVoltage();
    float battery_voltage_v = battery_voltage_mv / 1000.0f;
    printf("Battery Voltage: %.3fV (%dmV)\r\n", battery_voltage_v, battery_voltage_mv);

    // 4. LED闪烁3次
    printf("LED blinking 3 times...\r\n");
    for(int i = 0; i < 3; i++) {
        LED_TOGGLE;
        HAL_Delay(200);
        LED_TOGGLE;
        HAL_Delay(200);
    }

    // 5. 重新上电并初始化MLX90393
    printf("Initializing MLX90393...\r\n");
    MLX90393_PW_ON;
    HAL_Delay(100); // 等待上电稳定

    if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
        printf("MLX90393 Init: SUCCESS\r\n");

        // 首先配置超低功耗模式
        if (MLX90393_EnterLowPowerMode(&mlx_handle) == MLX90393_OK) {
            printf("MLX90393 Low Power Mode: ENABLED\r\n");
        } else {
            printf("MLX90393 Low Power Config: FAILED\r\n");
        }

        // 然后配置WOC模式（唤醒变化模式）
        if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
            printf("MLX90393 WOC Mode: ENABLED\r\n");

            // 关键：读取一次数据让MLX90393进入正常工作状态
            // 这可以清除任何挂起的中断状态，避免假死
            MLX90393_Data_t dummy_data;
            HAL_Delay(100);  // 等待WOC模式稳定

            if (MLX90393_ReadSingleMeasurement(&mlx_handle, &dummy_data) == MLX90393_OK) {
                printf("MLX90393 Initial Read: X=%.1f, Y=%.1f, Z=%.1f\r\n",
                       dummy_data.x, dummy_data.y, dummy_data.z);
                printf("MLX90393 ready for WOC interrupts\r\n");
            } else {
                printf("MLX90393 Initial Read: FAILED\r\n");
            }

            printf("INT pin (PB0) ready for interrupts\r\n");
        } else {
            printf("MLX90393 WOC Config: FAILED\r\n");
        }
    } else {
        printf("MLX90393 Init: FAILED\r\n");
    }

    // 6. 准备进入休眠模式
    printf("Preparing to enter STOP mode...\r\n");
    printf("Device will wake up on MLX90393 interrupt (PB0)\r\n");
    HAL_Delay(500); // 让串口输出完成

    // 7. 准备进入低功耗模式 - 采用FreeRTOS示例的彻底方法

    // 关闭所有外部模块电源
    RF_PWR_OFF;   // 关闭通信模块

    // 停止并禁用UART中断
    HAL_UART_AbortReceive_IT(&huart2);
    HAL_UART_AbortReceive_IT(&hlpuart1);

    // 完全停止ADC和DMA
    HAL_ADC_Stop_DMA(&hadc);
    HAL_ADC_Stop(&hadc);
    if(hadc.DMA_Handle != NULL) {
        HAL_DMA_Abort(hadc.DMA_Handle);
    }

    // 停止I2C传输
    HAL_I2C_Master_Abort_IT(&hi2c1, MLX90393_I2C_ADDR_SHIFTED);

    // 特殊处理：配置通信模块相关引脚为低功耗状态

    // 将M0、M1、M2配置为输入模式（避免输出驱动消耗功耗）
    GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 将LPUART1引脚配置为输入模式
    GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;  // PA2(TX), PA3(RX)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 额外优化：配置其他可能的漏电流源
    // 将USART2引脚也配置为输入模式
    GPIO_InitStruct.Pin = GPIO_PIN_15;  // PA15(USART2_RX)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_6;   // PB6(USART2_TX)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 将I2C引脚配置为输入模式
    GPIO_InitStruct.Pin = GPIO_PIN_9 | GPIO_PIN_10;  // PB9(SCL), PB10(SDA)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    printf("Communication module pins configured for low power\r\n");

    // 关闭外设时钟
    __HAL_RCC_USART2_CLK_DISABLE();
    __HAL_RCC_LPUART1_CLK_DISABLE();
    __HAL_RCC_ADC1_CLK_DISABLE();
    __HAL_RCC_DMA1_CLK_DISABLE();
    __HAL_RCC_I2C1_CLK_DISABLE();

    // 确保UART传输完成
    while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
    while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
    HAL_Delay(10);

    // 调试：检查寄存器状态
    printf("Debug: RCC->AHBENR = 0x%08lX\r\n", RCC->AHBENR);
    printf("Debug: RCC->APB1ENR = 0x%08lX\r\n", RCC->APB1ENR);
    printf("Debug: RCC->APB2ENR = 0x%08lX\r\n", RCC->APB2ENR);
    printf("Debug: NVIC pending: 0x%08lX\r\n", NVIC->ISPR[0]);

    // 等待串口输出完成
    while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
    HAL_Delay(50);

    // 8. 彻底的低功耗模式进入（参考FreeRTOS示例）

    // 禁用所有中断，准备进入低功耗模式
    __disable_irq();

    // 禁用SysTick中断
    SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

    // 禁用所有中断，只保留EXTI0_1唤醒中断
    for (uint8_t i = 0; i < 8; i++) {
        NVIC->ICER[i] = 0xFFFFFFFF;
    }

    // 确保EXTI0_1中断被启用（PB0唤醒中断）
    HAL_NVIC_SetPriority(EXTI0_1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(EXTI0_1_IRQn);

    // 清除所有挂起的中断
    for (uint8_t i = 0; i < 8; i++) {
        NVIC->ICPR[i] = 0xFFFFFFFF;
    }

    // 清除PWR标志
    __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

    // 重新启用全局中断，但只有EXTI0_1中断处于活动状态
    __enable_irq();

    // 进入STOP模式，使用低功耗调节器
    HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

    // ==================== 唤醒后恢复（参考FreeRTOS示例）====================

    // 打开LED表示唤醒开始
    LED_TOGGLE;

    // 重新配置系统时钟 - STOP模式后必需
    SystemClock_Config();

    // 重新启用必要的外设时钟
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_LPUART1_CLK_ENABLE();
    __HAL_RCC_ADC1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();
    __HAL_RCC_I2C1_CLK_ENABLE();

    // 重新配置GPIO（恢复正常功能）
    MX_GPIO_Init();

    // 重新初始化外设
    MX_USART2_UART_Init();
    MX_LPUART1_UART_Init();
    MX_ADC_Init();
    MX_I2C1_Init();

    // 确保UART完全重新初始化
    HAL_Delay(10);

    // 重新启动UART接收中断（如果需要）
    // HAL_UART_Receive_IT(&huart2, uart_rx_buffer, 1);  // 如果使用中断接收

    // 特殊恢复：重新配置通信模块引脚为正常工作状态
    // 复用之前声明的GPIO_InitStruct变量

    // 恢复M0、M1、M2为输出模式
    GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 设置M0、M1、M2为低电平（根据通信模块需要调整）
    HAL_GPIO_WritePin(GPIOA, M0_Pin | M1_Pin | M2_Pin, GPIO_PIN_RESET);

    printf("Communication module pins restored\r\n");

    // 重新启用SysTick中断
    SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

    // 重新启用必要的中断
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
    HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(LPUART1_IRQn);

    // LED闪烁表示正在唤醒
    LED_TOGGLE;
    HAL_Delay(100);
    LED_TOGGLE;

    printf("\r\n*** WAKE UP from STOP mode ***\r\n");

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_SYSCLK;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
