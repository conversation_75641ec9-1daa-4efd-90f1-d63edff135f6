/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
static MLX90393_Handle_t mlx_handle;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Low Power System ===\r\n");
	printf("System will enter STOP mode after initialization\r\n");
	printf("Wake up by MLX90393 INT signal on PB0\r\n");
	printf("=====================================\r\n");

	// 初始化时关闭MLX90393电源（确保干净的启动状态）
	MLX90393_PW_OFF;
	printf("MLX90393 Power: OFF (Initial state)\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // ==================== 工作流程开始 ====================
    printf("\r\n=== Device Wake Up ===\r\n");

    // 1. 立即关闭MLX90393电源（如果之前触发了中断）
		RF_PWR_ON; //打开通信模块

    MLX90393_PW_OFF;
    printf("MLX90393 Power: OFF\r\n");

    // 2. 等待1秒让ADC稳定（MLX90393断电时间可根据需要调整）
    printf("Waiting 1 second for ADC stabilization...\r\n");
    HAL_Delay(3000);

    // 3. 采集ADC电压值
    uint16_t battery_voltage_mv = ADC_ReadBatteryVoltage();
    float battery_voltage_v = battery_voltage_mv / 1000.0f;
    printf("Battery Voltage: %.3fV (%dmV)\r\n", battery_voltage_v, battery_voltage_mv);

    // 4. LED闪烁3次
    printf("LED blinking 3 times...\r\n");
    for(int i = 0; i < 3; i++) {
        LED_TOGGLE;
        HAL_Delay(200);
        LED_TOGGLE;
        HAL_Delay(200);
    }

    // 5. 重新上电并初始化MLX90393
    printf("Initializing MLX90393...\r\n");
    MLX90393_PW_ON;
    HAL_Delay(100); // 等待上电稳定

    if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
        printf("MLX90393 Init: SUCCESS\r\n");

        // 配置WOC模式（唤醒变化模式）
        if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
            printf("MLX90393 WOC Mode: ENABLED\r\n");
            printf("INT pin (PB0) ready for interrupts\r\n");
        } else {
            printf("MLX90393 WOC Config: FAILED\r\n");
        }
    } else {
        printf("MLX90393 Init: FAILED\r\n");
    }

    // 6. 准备进入休眠模式
		RF_PWR_OFF; //关闭通信模块

    printf("Preparing to enter STOP mode...\r\n");
    printf("Device will wake up on MLX90393 interrupt (PB0)\r\n");
    HAL_Delay(500); // 让串口输出完成

    // 7. 关闭不必要的外设时钟以节省功耗
    __HAL_RCC_USART2_CLK_DISABLE();
    __HAL_RCC_LPUART1_CLK_DISABLE();
    __HAL_RCC_ADC1_CLK_DISABLE();
    __HAL_RCC_DMA1_CLK_DISABLE();

    // 8. 进入STOP模式，等待外部中断唤醒
    HAL_SuspendTick();  // 暂停SysTick
    HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

    // ==================== 唤醒后恢复 ====================
    // 唤醒后重新配置系统时钟（STOP模式后必需）
    SystemClock_Config();
    HAL_ResumeTick();   // 恢复SysTick

    // 重新启用必要的外设时钟
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_LPUART1_CLK_ENABLE();
    __HAL_RCC_ADC1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();

    // 重新初始化UART（确保串口正常工作）
    MX_USART2_UART_Init();
    MX_LPUART1_UART_Init();

    printf("\r\n*** WAKE UP from STOP mode ***\r\n");

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_SYSCLK;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
