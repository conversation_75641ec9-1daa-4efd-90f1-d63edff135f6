/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    mlx90393.h
  * @brief   MLX90393 三轴磁场传感器驱动头文件
  ******************************************************************************
  * @attention
  *
  * MLX90393 是一款高精度的三轴磁场传感器，支持I2C通信
  * 本驱动实现基本的磁场测量功能
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __MLX90393_H__
#define __MLX90393_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"

/* MLX90393 I2C地址定义 */
#define MLX90393_I2C_ADDR           0x0C    // 默认I2C地址 (7位地址)
#define MLX90393_I2C_ADDR_SHIFTED   (MLX90393_I2C_ADDR << 1)  // HAL库需要8位地址

/* 根据示例代码，尝试不同的地址 */
#define MLX90393_I2C_ADDR_ALT       0x18    // 示例中使用的地址
#define MLX90393_I2C_ADDR_ALT_SHIFTED (MLX90393_I2C_ADDR_ALT << 1)

/* 可调整的传感器配置宏定义 */
// 增益设置 (0-7): 0=5x, 1=4x, 2=3x, 3=2.5x, 4=2x, 5=1.67x, 6=1.33x, 7=1x
#define MLX90393_GAIN_SEL           1       // 当前使用1.67x增益

// 分辨率设置 (0-3): 0=16bit, 1=15bit, 2=14bit, 3=13bit
#define MLX90393_RES_X              0       // X轴分辨率
#define MLX90393_RES_Y              0       // Y轴分辨率
#define MLX90393_RES_Z              0       // Z轴分辨率

// 过采样率设置 (0-3): 0=1x, 1=2x, 2=4x, 3=8x
#define MLX90393_OSR                0       // 过采样率

// 数字滤波设置 (0-7)
#define MLX90393_DIG_FILT           0       // 数字滤波

// 数据处理设置
#define MLX90393_X_SHIFT            0       // X轴数据右移位数
#define MLX90393_Y_SHIFT            0       // Y轴数据右移位数
#define MLX90393_Z_SHIFT            0       // Z轴数据右移位数

// X轴偏移补偿 (去掉固定的高位偏移)
#define MLX90393_X_OFFSET_ENABLE    0       // 启用X轴偏移补偿
#define MLX90393_X_OFFSET_VALUE     0   // X轴固定偏移值

// 唤醒变化模式(WOC)配置 - 可调整参数
#define MLX90393_WOC_ENABLE         1       // 启用WOC模式 (1=启用, 0=禁用)
#define MLX90393_WOC_XY_THRESHOLD   100     // X/Y轴变化阈值 (50-500 LSB, 越小越敏感)
#define MLX90393_WOC_Z_THRESHOLD    100     // Z轴变化阈值 (50-500 LSB, 越小越敏感)
#define MLX90393_WOC_T_THRESHOLD    100     // 温度变化阈值 (LSB)

// WOC监测轴选择 (可组合使用)
#define MLX90393_WOC_AXIS_X         0x08    // 监测X轴
#define MLX90393_WOC_AXIS_Y         0x04    // 监测Y轴
#define MLX90393_WOC_AXIS_Z         0x02    // 监测Z轴
#define MLX90393_WOC_AXIS_T         0x01    // 监测温度

// 默认监测轴组合 (可修改) - 轴测试模式
//#define MLX90393_WOC_AXES           MLX90393_WOC_AXIS_X                                              // 只测试X轴
//#define MLX90393_WOC_AXES           MLX90393_WOC_AXIS_Y                                              // 只测试Y轴
//#define MLX90393_WOC_AXES           MLX90393_WOC_AXIS_Z                                              // 只测试Z轴
//#define MLX90393_WOC_AXES           (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y)                      // 测试X+Y轴
#define MLX90393_WOC_AXES           (MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z | MLX90393_WOC_AXIS_X)  // X+Y+Z三轴

// WOC灵敏度预设 (选择其中一个)
#define MLX90393_WOC_SENSITIVITY_HIGH    // 高灵敏度: 阈值50
//#define MLX90393_WOC_SENSITIVITY_MEDIUM  // 中等灵敏度: 阈值200
//#define MLX90393_WOC_SENSITIVITY_LOW     // 低灵敏度: 阈值500

#ifdef MLX90393_WOC_SENSITIVITY_HIGH
    #undef MLX90393_WOC_XY_THRESHOLD
    #undef MLX90393_WOC_Z_THRESHOLD
    #define MLX90393_WOC_XY_THRESHOLD   100
    #define MLX90393_WOC_Z_THRESHOLD    100
#elif defined(MLX90393_WOC_SENSITIVITY_MEDIUM)
    #undef MLX90393_WOC_XY_THRESHOLD
    #undef MLX90393_WOC_Z_THRESHOLD
    #define MLX90393_WOC_XY_THRESHOLD   200
    #define MLX90393_WOC_Z_THRESHOLD    200
#elif defined(MLX90393_WOC_SENSITIVITY_LOW)
    #undef MLX90393_WOC_XY_THRESHOLD
    #undef MLX90393_WOC_Z_THRESHOLD
    #define MLX90393_WOC_XY_THRESHOLD   500
    #define MLX90393_WOC_Z_THRESHOLD    500
#endif

/* MLX90393 命令定义 */
#define MLX90393_CMD_NOP            0x00    // 空操作命令
#define MLX90393_CMD_EXIT           0x80    // 退出模式命令
#define MLX90393_CMD_START_BURST    0x10    // 开始突发模式
#define MLX90393_CMD_WOC            0x20    // 唤醒变化模式 (Wake-up On Change)
#define MLX90393_CMD_START_MEASURE  0x30    // 开始测量
#define MLX90393_CMD_READ_MEASURE   0x40    // 读取测量结果
#define MLX90393_CMD_READ_REG       0x50    // 读取寄存器
#define MLX90393_CMD_WRITE_REG      0x60    // 写入寄存器
#define MLX90393_CMD_MEMORY_RECALL  0xD0    // 内存回调
#define MLX90393_CMD_MEMORY_STORE   0xE0    // 内存存储
#define MLX90393_CMD_RESET          0xF0    // 复位命令

/* 测量轴选择位 - 根据寄存器文档zyxt格式修正 */
#define MLX90393_AXIS_T             0x01    // bit0 = 温度
#define MLX90393_AXIS_Z             0x02    // bit1 = Z轴
#define MLX90393_AXIS_Y             0x04    // bit2 = Y轴
#define MLX90393_AXIS_X             0x08    // bit3 = X轴
#define MLX90393_AXIS_ALL           (MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z | MLX90393_AXIS_T)

/* 状态字节位定义 */
#define MLX90393_STATUS_BURST_MODE  0x80    // 突发模式状态
#define MLX90393_STATUS_WOC_MODE    0x40    // 唤醒变化模式状态
#define MLX90393_STATUS_SM_MODE     0x20    // 单次测量模式状态
#define MLX90393_STATUS_ERROR       0x10    // 错误状态
#define MLX90393_STATUS_SED         0x08    // 单次错误检测
#define MLX90393_STATUS_RS          0x04    // 复位状态
#define MLX90393_STATUS_D1          0x02    // 数据就绪1
#define MLX90393_STATUS_D0          0x01    // 数据就绪0

/* 函数返回状态码定义 */
#define MLX90393_OK                 0       // 操作成功
#define MLX90393_ERROR              1       // 操作失败
#define MLX90393_TIMEOUT            1000    // I2C超时时间 (ms)

/* 寄存器地址定义 */
#define MLX90393_REG_CTRL1          0x00    // 控制寄存器1
#define MLX90393_REG_CTRL2          0x01    // 控制寄存器2
#define MLX90393_REG_CTRL3          0x02    // 控制寄存器3

/* 数据结构定义 */
typedef struct {
    float x;        // X轴磁场强度 (uT)
    float y;        // Y轴磁场强度 (uT)
    float z;        // Z轴磁场强度 (uT)
    float temp;     // 温度 (°C)
} MLX90393_Data_t;

typedef struct {
    I2C_HandleTypeDef *hi2c;   // I2C句柄
    uint8_t address;           // 设备I2C地址
    uint8_t gain_sel;          // 增益选择
    uint8_t res_x;             // X轴分辨率
    uint8_t res_y;             // Y轴分辨率
    uint8_t res_z;             // Z轴分辨率
    uint8_t osr;               // 过采样率
} MLX90393_Handle_t;

/* 函数声明 */
uint8_t MLX90393_Init(MLX90393_Handle_t *mlx, I2C_HandleTypeDef *hi2c);
uint8_t MLX90393_Reset(MLX90393_Handle_t *mlx);
uint8_t MLX90393_StartMeasurement(MLX90393_Handle_t *mlx, uint8_t axis);
uint8_t MLX90393_ReadMeasurement(MLX90393_Handle_t *mlx, MLX90393_Data_t *data, uint8_t axis);
uint8_t MLX90393_ReadSingleMeasurement(MLX90393_Handle_t *mlx, MLX90393_Data_t *data);
uint8_t MLX90393_ReadSingleMeasurement_Fixed(MLX90393_Handle_t *mlx, MLX90393_Data_t *data);
uint8_t MLX90393_ReadRegister(MLX90393_Handle_t *mlx, uint8_t reg_addr, uint16_t *reg_data);
uint8_t MLX90393_WriteRegister(MLX90393_Handle_t *mlx, uint8_t reg_addr, uint16_t reg_data);
uint8_t MLX90393_GetStatus(MLX90393_Handle_t *mlx);
void MLX90393_Test(void);
void MLX90393_DebugRawData(MLX90393_Handle_t *mlx);
void MLX90393_TestFixed(void);

/* WOC模式相关函数 */
uint8_t MLX90393_ConfigureWOC(MLX90393_Handle_t *mlx);
uint8_t MLX90393_EnterLowPowerMode(MLX90393_Handle_t *mlx);
uint8_t MLX90393_CheckWOCStatus(MLX90393_Handle_t *mlx);

/* 诊断和调试函数 */
void I2C_Scanner(I2C_HandleTypeDef *hi2c);
void MLX90393_DiagnosticTest(void);
uint8_t MLX90393_TestCommunication(MLX90393_Handle_t *mlx);
uint8_t MLX90393_ReadConfiguration(MLX90393_Handle_t *mlx);

/* 校准和统计函数 */
void MLX90393_CalibrateZero(MLX90393_Handle_t *mlx);
void MLX90393_ShowStatistics(void);

#ifdef __cplusplus
}
#endif

#endif /* __MLX90393_H__ */
